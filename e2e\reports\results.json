{"config": {"configFile": "D:\\AI PRojects\\ChatApplication\\playwright.config.ts", "rootDir": "D:/AI PRojects/ChatApplication/e2e/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "e2e/reports/html"}], ["json", {"outputFile": "e2e/reports/results.json"}], ["junit", {"outputFile": "e2e/reports/results.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.0", "workers": 6, "webServer": null}, "suites": [], "errors": [{"message": "Error: Requiring @playwright/test second time, \nFirst:\nError: \n    at Object.<anonymous> (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\index.js:60:33)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object.<anonymous> (node:internal/modules/cjs/loader:1895:10)\n    at Object.newLoader2 [as .js] (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\third_party\\pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\test.js:17:13)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object.<anonymous> (node:internal/modules/cjs/loader:1895:10)\n    at Object.newLoader2 [as .js] (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\third_party\\pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\AI PRojects\\ChatApplication\\node_modules\\@playwright\\test\\index.js:17:18)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object.<anonymous> (node:internal/modules/cjs/loader:1895:10)\n    at Object.newLoader2 [as .js] (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\third_party\\pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\AI PRojects\\ChatApplication\\playwright.config.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Module.newCompile2 (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\third_party\\pirates.js:46:29)\n    at Object.<anonymous> (node:internal/modules/cjs/loader:1895:10)\n    at Object.newLoader2 [as .ts] (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\third_party\\pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at requireOrImport (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\transform\\transform.js:218:18)\n    at loadUserConfig (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\common\\configLoader.js:107:89)\n    at loadConfig (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\common\\configLoader.js:119:28)\n    at loadConfigFromFile (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\common\\configLoader.js:331:10)\n    at runTests (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\program.js:145:18)\n    at i.<anonymous> (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\program.js:64:7)\n\nSecond: ", "stack": "Error: Requiring @playwright/test second time, \nFirst:\nError: \n    at Object.<anonymous> (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\index.js:60:33)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object.<anonymous> (node:internal/modules/cjs/loader:1895:10)\n    at Object.newLoader2 [as .js] (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\third_party\\pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\test.js:17:13)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object.<anonymous> (node:internal/modules/cjs/loader:1895:10)\n    at Object.newLoader2 [as .js] (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\third_party\\pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\AI PRojects\\ChatApplication\\node_modules\\@playwright\\test\\index.js:17:18)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object.<anonymous> (node:internal/modules/cjs/loader:1895:10)\n    at Object.newLoader2 [as .js] (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\third_party\\pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\AI PRojects\\ChatApplication\\playwright.config.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Module.newCompile2 (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\third_party\\pirates.js:46:29)\n    at Object.<anonymous> (node:internal/modules/cjs/loader:1895:10)\n    at Object.newLoader2 [as .ts] (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\third_party\\pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at requireOrImport (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\transform\\transform.js:218:18)\n    at loadUserConfig (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\common\\configLoader.js:107:89)\n    at loadConfig (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\common\\configLoader.js:119:28)\n    at loadConfigFromFile (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\common\\configLoader.js:331:10)\n    at runTests (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\program.js:145:18)\n    at i.<anonymous> (D:\\AI PRojects\\ChatApplication\\node_modules\\playwright\\lib\\program.js:64:7)\n\nSecond: \n    at Object.<anonymous> (D:\\AI PRojects\\ChatApplication\\node_modules\\@playwright\\test\\index.js:17:18)\n    at Object.<anonymous> (D:\\AI PRojects\\ChatApplication\\playwright.config.ts:1:1)\n    at Object.<anonymous> (D:\\AI PRojects\\ChatApplication\\e2e\\node_modules\\playwright\\lib\\index.js:55:11)\n    at Object.<anonymous> (D:\\AI PRojects\\ChatApplication\\e2e\\node_modules\\playwright\\test.js:17:13)\n    at Object.<anonymous> (D:\\AI PRojects\\ChatApplication\\e2e\\node_modules\\@playwright\\test\\index.js:17:18)\n    at Object.<anonymous> (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\conversation-creation-notification-fix.spec.ts:2:1)", "location": {"file": "D:\\AI PRojects\\ChatApplication\\playwright.config.ts", "column": 1, "line": 1}, "snippet": "\u001b[90m   at \u001b[39m..\\..\\playwright.config.ts:1\n\n\u001b[0m\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 1 |\u001b[39m \u001b[36mimport\u001b[39m { defineConfig\u001b[33m,\u001b[39m devices } \u001b[36mfrom\u001b[39m \u001b[32m'@playwright/test'\u001b[39m\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 2 |\u001b[39m\n \u001b[90m 3 |\u001b[39m \u001b[90m/**\u001b[39m\n \u001b[90m 4 |\u001b[39m \u001b[90m * @see https://playwright.dev/docs/test-configuration\u001b[39m\u001b[0m"}, {"message": "Error: No tests found.\nMake sure that arguments are regular expressions matching test files.\nYou may need to escape symbols like \"$\" or \"*\" and quote the arguments.", "stack": "Error: No tests found.\nMake sure that arguments are regular expressions matching test files.\nYou may need to escape symbols like \"$\" or \"*\" and quote the arguments."}], "stats": {"startTime": "2025-09-06T06:26:55.864Z", "duration": 462.5580000000001, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}