# backend/messaging/views.py
from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.shortcuts import get_object_or_404
from django.http import Http404
from django.db.models import Q, Max
from django.utils import timezone
from django.contrib.auth import get_user_model
from pydantic_core import ValidationError
from .models import Conversation, ConversationParticipant, Message
from .schemas import (
    ConversationCreate, ConversationResponse, MessageCreate,
    MessageResponse, UserBasic, UserWithEncryption
)
from encryption.models import UserKeyBundle
User = get_user_model()

class MessagePagination(PageNumberPagination):
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 100

def serialize_user(user):
    """Convert Django User model to Pydantic UserBasic"""
    return UserBasic(
        id=user.id,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name,
        profile_picture=user.profile_picture
    )

def serialize_message(message, request_user=None):
    """Convert Django Message model to Pydantic MessageResponse with encryption support"""
    # Get message status for the requesting user
    status = None
    if request_user and request_user.is_authenticated:
        try:
            message_status = message.statuses.filter(user=request_user).first()
            if message_status:
                status = message_status.status
            elif message.sender == request_user:
                # For own messages, default to SENT if no status exists
                status = 'SENT'
        except Exception:
            pass

    # Check if message is encrypted
    is_encrypted = message.is_encrypted

    # Prepare response data
    response_data = {
        'id': message.id,
        'conversation_id': message.conversation.id,
        'sender': serialize_user(message.sender),
        'message_type': message.message_type,
        'created_at': message.created_at,
        'updated_at': message.updated_at,
        'status': status,
        'is_encrypted': is_encrypted,
    }

    if is_encrypted:
        # For encrypted messages, include encryption fields
        response_data.update({
            'encrypted_content': message.encrypted_content,
            'iv': message.iv,
            'sender_ratchet_key': message.sender_ratchet_key,
            'message_number': message.message_number,
            'previous_chain_length': message.previous_chain_length,
        })
    else:
        # For plaintext messages, include content
        response_data['content'] = message.content

    return MessageResponse(**response_data)

def serialize_conversation(conversation, request_user=None, include_encryption_status=False):
    """Convert Django Conversation model to Pydantic ConversationResponse with encryption support"""
    participants = [
        serialize_user(p.user) for p in conversation.participants.all()
    ]

    last_message = conversation.messages.last()
    last_message_data = serialize_message(last_message, request_user) if last_message else None

    # Base response data
    response_data = {
        'id': conversation.id,
        'type': conversation.type,
        'name': conversation.name,
        'participants': participants,
        'last_message': last_message_data,
        'created_at': conversation.created_at,
        'updated_at': conversation.updated_at,
    }

    # Add encryption status if requested
    if include_encryption_status:
        # Check encryption status for all participants
        encryption_participants = []
        all_have_encryption = True

        for participant in conversation.participants.all():
            has_encryption = hasattr(participant.user, 'key_bundle') and participant.user.key_bundle is not None
            if not has_encryption:
                all_have_encryption = False

            encryption_participants.append(UserWithEncryption(
                id=participant.user.id,
                username=participant.user.username,
                first_name=participant.user.first_name,
                last_name=participant.user.last_name,
                profile_picture=participant.user.profile_picture,
                has_encryption=has_encryption
            ))

        response_data.update({
            'is_encrypted': all_have_encryption,
            'encryption_participants': encryption_participants,
        })

    return ConversationResponse(**response_data)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def list_conversations(request):
    """List user's conversations"""
    conversations = Conversation.objects.filter(
        participants__user=request.user
    ).distinct().order_by('-updated_at')

    paginator = MessagePagination()
    page = paginator.paginate_queryset(conversations, request)

    # Include encryption status in conversation list
    conversation_data = [
        serialize_conversation(conv, request.user, include_encryption_status=True).model_dump(by_alias=True)
        for conv in page
    ]

    return paginator.get_paginated_response(conversation_data)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_conversation(request):
    """Create a new conversation"""
    try:
        # Validate input with Pydantic
        conversation_data = ConversationCreate(**request.data)

        # For direct messages, check if conversation already exists
        if conversation_data.type == 'DIRECT' and len(conversation_data.participant_ids) == 1:
            other_user_id = conversation_data.participant_ids[0]
            existing_conversation = Conversation.objects.filter(
                type='DIRECT',
                participants__user=request.user
            ).filter(
                participants__user_id=other_user_id
            ).first()

            if existing_conversation:
                # Send notification to the other participant about the existing conversation
                # This ensures that if Alice creates a conversation with Harry, Harry gets notified
                # even if the conversation already exists
                try:
                    from django.conf import settings
                    import requests

                    # Prepare conversation data for socket notification
                    conversation_data_for_socket = serialize_conversation(existing_conversation, request.user).model_dump(by_alias=True)

                    # Get all participant IDs including creator
                    all_participant_ids = [str(request.user.id), str(other_user_id)]

                    # Send notification to socket server
                    socket_payload = {
                        'event': 'conversation_created',
                        'conversation': conversation_data_for_socket,
                        'participant_ids': all_participant_ids,
                        'creator_id': str(request.user.id)
                    }

                    # Make request to socket server to notify participants
                    socket_url = getattr(settings, 'SOCKET_SERVER_URL', 'http://localhost:7000')
                    requests.post(
                        f'{socket_url}/api/notify-conversation-created',
                        json=socket_payload,
                        timeout=5
                    )
                    print(f"Sent conversation notification for existing conversation {existing_conversation.id}")
                except Exception as e:
                    # Log error but don't fail the conversation return
                    print(f"Failed to send existing conversation notification: {e}")

                return Response(
                    serialize_conversation(existing_conversation, request.user).model_dump(by_alias=True),
                    status=status.HTTP_200_OK
                )

        # Verify all participant IDs exist
        participant_users = User.objects.filter(id__in=conversation_data.participant_ids)
        if participant_users.count() != len(conversation_data.participant_ids):
            return Response(
                {'error': 'One or more participant IDs are invalid'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create new conversation
        conversation = Conversation.objects.create(
            type=conversation_data.type,
            name=conversation_data.name if conversation_data.type == 'GROUP' else None
        )

        # Add creator as participant
        ConversationParticipant.objects.create(
            conversation=conversation,
            user=request.user,
            role='ADMIN' if conversation_data.type == 'GROUP' else 'MEMBER'
        )

        # Add other participants
        for user_id in conversation_data.participant_ids:
            if user_id != request.user.id:
                ConversationParticipant.objects.create(
                    conversation=conversation,
                    user_id=user_id,
                    role='MEMBER'
                )

        # Send real-time notification to all participants about new conversation
        from django.conf import settings
        import requests
        import json

        try:
            # Prepare conversation data for socket notification
            conversation_data_for_socket = serialize_conversation(conversation, request.user).model_dump(by_alias=True)

            # Get all participant IDs including creator
            all_participant_ids = [str(request.user.id)] + [str(uid) for uid in conversation_data.participant_ids]

            # Send notification to socket server
            socket_payload = {
                'event': 'conversation_created',
                'conversation': conversation_data_for_socket,
                'participant_ids': all_participant_ids,
                'creator_id': str(request.user.id)
            }

            # Make request to socket server to notify participants
            socket_url = getattr(settings, 'SOCKET_SERVER_URL', 'http://localhost:7000')
            requests.post(
                f'{socket_url}/api/notify-conversation-created',
                json=socket_payload,
                timeout=5
            )
        except Exception as e:
            # Log error but don't fail the conversation creation
            print(f"Failed to send conversation creation notification: {e}")

        return Response(
            serialize_conversation(conversation, request.user).model_dump(by_alias=True),
            status=status.HTTP_201_CREATED
        )

    except ValidationError as e:
        # Convert validation errors to a JSON-serializable format
        errors = []
        for error in e.errors():
            error_dict = {
                'type': error.get('type'),
                'loc': error.get('loc', []),
                'msg': error.get('msg'),
                'input': str(error.get('input', ''))
            }
            errors.append(error_dict)
        return Response({'errors': errors}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_conversation_messages(request, conversation_id):
    """Get messages for a conversation"""
    conversation = get_object_or_404(Conversation, id=conversation_id)

    # Check if user is participant
    if not conversation.participants.filter(user=request.user).exists():
        return Response(
            {'error': 'Not a participant in this conversation'},
            status=status.HTTP_403_FORBIDDEN
        )

    messages = conversation.messages.order_by('-created_at')

    paginator = MessagePagination()
    page = paginator.paginate_queryset(messages, request)

    message_data = [serialize_message(msg, request.user).model_dump(by_alias=True) for msg in page]

    return paginator.get_paginated_response(message_data)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def send_message(request, conversation_id):
    """Send a message to a conversation"""
    try:
        conversation = get_object_or_404(Conversation, id=conversation_id)

        # Check if user is participant
        participant = conversation.participants.filter(user=request.user).first()
        if not participant:
            return Response(
                {'error': 'Not a participant in this conversation'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Validate input with Pydantic
        message_data = MessageCreate(**request.data)

        # Determine if this is an encrypted message
        is_encrypted = bool(message_data.encrypted_content)

        # Prepare message creation data
        message_create_data = {
            'conversation': conversation,
            'sender': request.user,
            'message_type': message_data.message_type,
        }

        if is_encrypted:
            # For encrypted messages
            message_create_data.update({
                'content': '',  # Empty for encrypted messages
                'encrypted_content': message_data.encrypted_content,
                'iv': message_data.iv,
                'sender_ratchet_key': message_data.sender_ratchet_key,
                'message_number': message_data.message_number,
                'previous_chain_length': message_data.previous_chain_length,
            })
            print(f"Creating encrypted message for conversation {conversation_id} by user {request.user.id}")
        else:
            # For plaintext messages
            message_create_data.update({
                'content': message_data.content,
                'encrypted_content': '',
                'iv': '',
                'sender_ratchet_key': '',
                'message_number': 0,
                'previous_chain_length': 0,
            })
            print(f"Creating plaintext message for conversation {conversation_id} by user {request.user.id}")

        # Create message
        message = Message.objects.create(**message_create_data)

        # Update conversation timestamp
        conversation.updated_at = timezone.now()
        conversation.save(update_fields=['updated_at'])

        # Note: Real-time emission will be handled by the socket server
        # when it detects the new message in the database

        return Response(
            serialize_message(message, request.user).model_dump(by_alias=True),
            status=status.HTTP_201_CREATED
        )

    except Http404:
        return Response({'error': 'Conversation not found'}, status=status.HTTP_404_NOT_FOUND)
    except ValidationError as e:
        # Convert validation errors to a JSON-serializable format
        errors = []
        for error in e.errors():
            error_dict = {
                'type': error.get('type'),
                'loc': error.get('loc', []),
                'msg': error.get('msg'),
                'input': str(error.get('input', ''))
            }
            errors.append(error_dict)
        return Response({'errors': errors}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_conversation_encryption_status(request, conversation_id):
    """Get encryption status for a conversation"""
    try:
        conversation = get_object_or_404(Conversation, id=conversation_id)

        # Check if user is participant
        if not conversation.participants.filter(user=request.user).exists():
            return Response(
                {'error': 'Not a participant in this conversation'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get detailed encryption status
        conversation_data = serialize_conversation(
            conversation,
            request.user,
            include_encryption_status=True
        )

        return Response({
            'conversation_id': conversation.id,
            'is_encrypted': conversation_data.is_encrypted,
            'participants': [
                {
                    'id': str(p.id),
                    'username': p.username,
                    'first_name': p.first_name,
                    'last_name': p.last_name,
                    'has_encryption': p.has_encryption,
                }
                for p in conversation_data.encryption_participants
            ]
        })

    except Http404:
        return Response({'error': 'Conversation not found'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def search_users(request):
    """Search for users by username, first name, or last name"""
    query = request.GET.get('q', '').strip()

    if not query:
        return Response({
            'success': True,
            'results': []
        }, status=status.HTTP_200_OK)

    if len(query) < 2:
        return Response({
            'success': False,
            'error': 'Search query must be at least 2 characters long'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        # Search users by username, first name, or last name
        # Exclude the current user from results
        users = User.objects.filter(
            Q(username__icontains=query) |
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query)
        ).exclude(
            id=request.user.id
        ).order_by('username')[:10]  # Limit to 10 results

        # Serialize users
        user_data = []
        for user in users:
            user_data.append({
                'id': str(user.id),
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'profile_picture': user.profile_picture,
                'full_name': f"{user.first_name} {user.last_name}".strip()
            })

        return Response({
            'success': True,
            'results': user_data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_user_profile(request, user_id):
    """Get a user's profile by ID"""
    try:
        # Get user by ID
        user = get_object_or_404(User, id=user_id)

        # Serialize user data
        user_data = {
            'id': str(user.id),
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'profile_picture': user.profile_picture,
            'full_name': f"{user.first_name} {user.last_name}".strip()
        }

        return Response({
            'success': True,
            'data': user_data
        }, status=status.HTTP_200_OK)

    except Http404:
        return Response({
            'success': False,
            'error': 'User not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# PHASE 4: Group Management APIs

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_group(request):
    """Create a new group conversation"""
    from django.db import transaction
    from .models import GroupEvent
    from encryption.group_utils import initialize_group_encryption

    try:
        data = request.data
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        member_ids = data.get('member_ids', [])
        max_participants = data.get('max_participants', 50)
        is_public = data.get('is_public', False)

        # Validation
        if not name:
            return Response({'error': 'Group name is required'}, status=status.HTTP_400_BAD_REQUEST)

        if len(member_ids) > 49:  # Creator + 49 others = 50 max
            return Response({'error': 'Too many initial members'}, status=status.HTTP_400_BAD_REQUEST)

        # Verify all user IDs exist
        if member_ids:
            existing_users = User.objects.filter(id__in=member_ids).count()
            if existing_users != len(member_ids):
                return Response({'error': 'One or more user IDs are invalid'}, status=status.HTTP_400_BAD_REQUEST)

        with transaction.atomic():
            # Create group conversation
            group = Conversation.objects.create(
                type='GROUP',
                name=name,
                description=description,
                created_by=request.user,
                max_participants=max_participants,
                is_public=is_public
            )

            # Add creator as admin
            ConversationParticipant.objects.create(
                conversation=group,
                user=request.user,
                role='admin'
            )

            # Add initial members
            for user_id in member_ids:
                if user_id != str(request.user.id):
                    ConversationParticipant.objects.create(
                        conversation=group,
                        user_id=user_id,
                        role='member'
                    )

                    # Create group event
                    GroupEvent.objects.create(
                        conversation=group,
                        event_type='member_added',
                        actor=request.user,
                        target_user_id=user_id
                    )

            # Initialize group encryption
            try:
                initialize_group_encryption(group)
            except Exception as e:
                print(f"Warning: Failed to initialize group encryption: {e}")

        # Serialize response
        group_data = serialize_conversation(group, request.user)
        return Response(group_data, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def add_group_member(request, conversation_id):
    """Add a member to a group"""
    from django.db import transaction
    from .models import GroupEvent
    from encryption.group_utils import add_member_to_group_encryption

    try:
        group = get_object_or_404(Conversation, id=conversation_id, type='GROUP')

        # Check permissions
        participant = group.participants.filter(user=request.user, is_active=True).first()
        if not participant or not participant.can_add_members:
            return Response({'error': 'Permission denied'}, status=status.HTTP_403_FORBIDDEN)

        user_id = request.data.get('user_id')
        role = request.data.get('role', 'member')

        if not user_id:
            return Response({'error': 'user_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user_to_add = User.objects.get(id=user_id)

            # Check if already a member
            if group.participants.filter(user=user_to_add, is_active=True).exists():
                return Response({'error': 'User is already a member'}, status=status.HTTP_400_BAD_REQUEST)

            # Check group capacity
            current_members = group.participants.filter(is_active=True).count()
            if current_members >= group.max_participants:
                return Response({'error': 'Group is at maximum capacity'}, status=status.HTTP_400_BAD_REQUEST)

            with transaction.atomic():
                # Add member
                new_participant = ConversationParticipant.objects.create(
                    conversation=group,
                    user=user_to_add,
                    role=role
                )

                # Create group event
                GroupEvent.objects.create(
                    conversation=group,
                    event_type='member_added',
                    actor=request.user,
                    target_user=user_to_add
                )

                # Update group encryption
                try:
                    add_member_to_group_encryption(group, user_to_add)
                except Exception as e:
                    print(f"Warning: Failed to add member to group encryption: {e}")

            return Response({
                'message': 'Member added successfully',
                'participant': {
                    'id': str(new_participant.id),
                    'user': serialize_user(user_to_add).dict(),
                    'role': new_participant.role,
                    'joined_at': new_participant.joined_at.isoformat()
                }
            })

        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['DELETE'])
@permission_classes([permissions.IsAuthenticated])
def remove_group_member(request, conversation_id, user_id):
    """Remove a member from a group"""
    from django.db import transaction
    from .models import GroupEvent
    from encryption.group_utils import rotate_group_keys

    try:
        group = get_object_or_404(Conversation, id=conversation_id, type='GROUP')

        # Check permissions
        participant = group.participants.filter(user=request.user, is_active=True).first()
        if not participant or not participant.can_remove_members:
            return Response({'error': 'Permission denied'}, status=status.HTTP_403_FORBIDDEN)

        try:
            member_to_remove = group.participants.get(user_id=user_id, is_active=True)

            # Cannot remove group creator unless transferring ownership
            if member_to_remove.user == group.created_by and member_to_remove.role == 'admin':
                admins_count = group.participants.filter(role='admin', is_active=True).count()
                if admins_count <= 1:
                    return Response({'error': 'Cannot remove the last admin'}, status=status.HTTP_400_BAD_REQUEST)

            with transaction.atomic():
                # Deactivate member
                member_to_remove.is_active = False
                member_to_remove.save()

                # Create group event
                GroupEvent.objects.create(
                    conversation=group,
                    event_type='member_removed',
                    actor=request.user,
                    target_user=member_to_remove.user
                )

                # Rotate group encryption keys
                try:
                    rotate_group_keys(group, reason='member_removed', rotated_by=request.user)
                except Exception as e:
                    print(f"Warning: Failed to rotate group keys: {e}")

            return Response({'message': 'Member removed successfully'})

        except ConversationParticipant.DoesNotExist:
            return Response({'error': 'Member not found'}, status=status.HTTP_404_NOT_FOUND)

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PUT'])
@permission_classes([permissions.IsAuthenticated])
def update_group_info(request, conversation_id):
    """Update group information"""
    from .models import GroupEvent

    try:
        group = get_object_or_404(Conversation, id=conversation_id, type='GROUP')

        # Check permissions
        participant = group.participants.filter(user=request.user, is_active=True).first()
        if not participant or not participant.can_edit_group_info:
            return Response({'error': 'Permission denied'}, status=status.HTTP_403_FORBIDDEN)

        data = request.data
        updated_fields = {}

        # Update allowed fields
        if 'name' in data:
            name = data['name'].strip()
            if not name:
                return Response({'error': 'Group name cannot be empty'}, status=status.HTTP_400_BAD_REQUEST)
            group.name = name
            updated_fields['name'] = name

        if 'description' in data:
            group.description = data['description'].strip()
            updated_fields['description'] = group.description

        if 'avatar_url' in data:
            group.avatar_url = data['avatar_url']
            updated_fields['avatar_url'] = group.avatar_url

        if 'group_settings' in data:
            group.group_settings = data['group_settings']
            updated_fields['group_settings'] = group.group_settings

        group.save()

        # Create group event
        GroupEvent.objects.create(
            conversation=group,
            event_type='group_info_updated',
            actor=request.user,
            event_data=updated_fields
        )

        group_data = serialize_conversation(group, request.user)
        return Response(group_data)

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def leave_group(request, conversation_id):
    """Leave a group conversation"""
    from django.db import transaction
    from .models import GroupEvent
    from encryption.group_utils import rotate_group_keys

    try:
        group = get_object_or_404(Conversation, id=conversation_id, type='GROUP')

        try:
            participant = group.participants.get(user=request.user, is_active=True)

            # Check if user is the last admin
            if participant.role == 'admin':
                admins_count = group.participants.filter(role='admin', is_active=True).count()
                if admins_count <= 1:
                    return Response({'error': 'Cannot leave group as the last admin. Transfer admin role first.'},
                                  status=status.HTTP_400_BAD_REQUEST)

            with transaction.atomic():
                # Deactivate participation
                participant.is_active = False
                participant.save()

                # Create group event
                GroupEvent.objects.create(
                    conversation=group,
                    event_type='member_left',
                    actor=request.user,
                    target_user=request.user
                )

                # Rotate group encryption keys
                try:
                    rotate_group_keys(group, reason='member_removed', rotated_by=request.user)
                except Exception as e:
                    print(f"Warning: Failed to rotate group keys: {e}")

            return Response({'message': 'Left group successfully'})

        except ConversationParticipant.DoesNotExist:
            return Response({'error': 'Not a member of this group'}, status=status.HTTP_404_NOT_FOUND)

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# PHASE 4: Group Encryption API Endpoints

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_group_keys(request, conversation_id):
    """Get unclaimed group keys for offline member"""
    from encryption.group_utils import get_unclaimed_group_keys

    try:
        conversation = get_object_or_404(Conversation, id=conversation_id, type='GROUP')

        # Verify user is a participant
        if not conversation.participants.filter(user=request.user, is_active=True).exists():
            return Response({'error': 'Not a group member'}, status=status.HTTP_403_FORBIDDEN)

        # Get unclaimed keys
        key_data = get_unclaimed_group_keys(request.user, conversation)

        if not key_data:
            return Response({'error': 'No unclaimed keys available'}, status=status.HTTP_404_NOT_FOUND)

        return Response({
            'group_key_data': key_data,
            'conversation_id': str(conversation.id),
            'current_epoch': getattr(conversation, 'group_session', None) and conversation.group_session.current_epoch or 0
        })

    except Exception as e:
        return Response({'error': 'Failed to retrieve group keys'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def claim_group_key_endpoint(request, conversation_id):
    """Mark a group key as claimed"""
    from encryption.group_utils import claim_group_key

    try:
        conversation = get_object_or_404(Conversation, id=conversation_id, type='GROUP')

        # Verify user is a participant
        if not conversation.participants.filter(user=request.user, is_active=True).exists():
            return Response({'error': 'Not a group member'}, status=status.HTTP_403_FORBIDDEN)

        key_id = request.data.get('key_id')
        if not key_id:
            return Response({'error': 'key_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        success = claim_group_key(request.user, key_id)

        if success:
            return Response({'message': 'Group key claimed successfully'})
        else:
            return Response({'error': 'Failed to claim group key'}, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({'error': 'Failed to claim group key'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def send_group_message(request, conversation_id):
    """Send a message to a group with signature"""
    from django.db import transaction
    from encryption.group_utils import create_group_message_signature

    try:
        conversation = get_object_or_404(Conversation, id=conversation_id, type='GROUP')

        # Verify user is a participant
        if not conversation.participants.filter(user=request.user, is_active=True).exists():
            return Response({'error': 'Not a group member'}, status=status.HTTP_403_FORBIDDEN)

        content = request.data.get('content')
        sender_private_key = request.data.get('sender_private_key')  # Client provides for signing

        if not content or not sender_private_key:
            return Response({'error': 'content and sender_private_key are required'},
                          status=status.HTTP_400_BAD_REQUEST)

        with transaction.atomic():
            # Create message
            message = Message.objects.create(
                conversation=conversation,
                sender=request.user,
                content=content,
                message_type='TEXT'
            )

            # Create signature for authentication
            try:
                signature = create_group_message_signature(message, sender_private_key)
            except Exception as e:
                print(f"Warning: Failed to create message signature: {e}")
                signature = None

        # Serialize and return
        message_data = serialize_message(message, request.user)
        if signature:
            message_data['signature'] = signature

        return Response(message_data, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({'error': 'Failed to send message'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def verify_message_signature(request, message_id):
    """Verify the signature of a group message"""
    from encryption.group_utils import verify_group_message_signature

    try:
        message = get_object_or_404(Message, id=message_id)

        # Verify user has access to this message
        if not message.conversation.participants.filter(user=request.user, is_active=True).exists():
            return Response({'error': 'Access denied'}, status=status.HTTP_403_FORBIDDEN)

        is_valid = verify_group_message_signature(message)

        return Response({
            'message_id': str(message.id),
            'signature_valid': is_valid,
            'sender': message.sender.username,
            'timestamp': message.created_at.isoformat()
        })

    except Exception as e:
        return Response({'error': 'Failed to verify signature'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def rotate_group_keys_manual(request, conversation_id):
    """Manually rotate group keys (admin only)"""
    from encryption.group_utils import rotate_group_keys

    try:
        conversation = get_object_or_404(Conversation, id=conversation_id, type='GROUP')

        # Check admin permissions
        participant = conversation.participants.filter(
            user=request.user,
            role='admin',
            is_active=True
        ).first()

        if not participant:
            return Response({'error': 'Admin permission required'}, status=status.HTTP_403_FORBIDDEN)

        # Rotate keys
        rotate_group_keys(conversation, reason='manual', rotated_by=request.user)

        return Response({
            'message': 'Group keys rotated successfully',
            'new_epoch': getattr(conversation, 'group_session', None) and conversation.group_session.current_epoch or 0
        })

    except Exception as e:
        return Response({'error': 'Failed to rotate group keys'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
